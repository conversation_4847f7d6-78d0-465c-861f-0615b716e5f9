# Button Usage Guidelines

## Overview

This document provides comprehensive guidelines for using the standardized Button component throughout the EthosPrompt application. All button implementations should use the `Button` component from `@/components/ui/button` to ensure consistency, accessibility, and maintainability.

## Button Variants

### 1. `default` - Primary Brand Button
**Use for:** Main actions, form submissions, primary CTAs
**Styling:** Purple background with white text
```tsx
<Button variant="default" size="default">
  Submit Form
</Button>
```

### 2. `cta` - Call-to-Action Button
**Use for:** Hero sections, landing page CTAs, conversion-focused buttons
**Styling:** Purple background, rounded-full, responsive sizing
```tsx
<Button variant="cta" size="cta">
  Get Started Now
</Button>
```

### 3. `gradient` - Gradient Button
**Use for:** Premium features, special promotions, standout actions
**Styling:** Purple to pink gradient with shadow effects
```tsx
<Button variant="gradient" size="lg">
  Schedule Demo
</Button>
```

### 4. `outline` - Secondary Button
**Use for:** Secondary actions, cancel buttons, alternative options
**Styling:** Transparent background with border
```tsx
<Button variant="outline" size="default">
  Cancel
</Button>
```

### 5. `secondary` - Alternative Secondary
**Use for:** Less prominent actions, supporting buttons
**Styling:** Gray background with darker text
```tsx
<Button variant="secondary" size="default">
  Learn More
</Button>
```

### 6. `ghost` - Minimal Button
**Use for:** Close buttons, subtle actions, icon buttons
**Styling:** Transparent background, hover effects only
```tsx
<Button variant="ghost" size="icon">
  <X className="w-4 h-4" />
</Button>
```

### 7. `link` - Text Link Style
**Use for:** Navigation links, inline actions, text-based CTAs
**Styling:** Underlined text, no background
```tsx
<Button variant="link" size="default">
  Read More
</Button>
```

### 8. `destructive` - Danger Actions
**Use for:** Delete actions, dangerous operations, error states
**Styling:** Red background with white text
```tsx
<Button variant="destructive" size="default">
  Delete Account
</Button>
```

## Button Sizes

### 1. `default` - Standard Size ✅ WCAG Compliant
**Use for:** Most common buttons, form controls
**Dimensions:** h-11 px-4 py-2 (44px height) - **Updated for accessibility compliance**
**Font Size:** 16px responsive to 18px (`text-base sm:text-lg`)
```tsx
<Button size="default">Standard Button</Button>
```

### 2. `sm` - Small Size ✅ WCAG Compliant
**Use for:** Compact interfaces, secondary actions, mobile optimization
**Dimensions:** h-11 px-3 (44px height) - **Updated for accessibility compliance**
**Font Size:** 14px responsive to 16px (`text-sm sm:text-base`)
```tsx
<Button size="sm">Small Button</Button>
```

### 3. `lg` - Large Size ✅ WCAG Compliant
**Use for:** Important actions, landing pages, prominent CTAs
**Dimensions:** h-12 px-8 (48px height) - **Enhanced for better accessibility**
**Font Size:** 16px responsive to 20px (`text-base sm:text-lg md:text-xl`)
```tsx
<Button size="lg">Large Button</Button>
```

### 4. `icon` - Icon Button ✅ WCAG Compliant
**Use for:** Icon-only buttons, close buttons, action buttons
**Dimensions:** h-11 w-11 (44×44px) - **Updated for accessibility compliance**
**Font Size:** 16px (`text-base`)
```tsx
<Button size="icon">
  <Settings className="w-4 h-4" />
</Button>
```

### 5. `cta` - CTA Size ✅ WCAG Compliant
**Use for:** Hero sections, conversion-focused buttons
**Dimensions:** Responsive sizing from h-12 to h-18 (48-72px)
**Font Size:** 16px responsive to 32px (`text-base sm:text-lg md:text-xl lg:text-2xl`)
```tsx
<Button variant="cta" size="cta">Call to Action</Button>
```

## Mobile-Optimized Button Variants

### New Mobile-First Variants

### 6. `mobile-primary` - Mobile Primary Action ✅ Mobile Optimized
**Use for:** Primary actions on mobile devices
**Styling:** Enhanced touch targets with 48px minimum size
```tsx
<Button variant="mobile-primary" size="mobile-default">Mobile Action</Button>
```

### 7. `mobile-secondary` - Mobile Secondary Action ✅ Mobile Optimized
**Use for:** Secondary actions on mobile devices
**Styling:** Enhanced touch targets with border styling
```tsx
<Button variant="mobile-secondary" size="mobile-default">Secondary</Button>
```

### 8. `mobile-cta` - Mobile CTA ✅ Mobile Optimized
**Use for:** Call-to-action buttons on mobile devices
**Styling:** Enhanced touch targets with brand styling
```tsx
<Button variant="mobile-cta" size="mobile-lg">Get Started</Button>
```

## Mobile-Optimized Button Sizes

### 9. `mobile-sm` - Mobile Small ✅ Mobile Optimized
**Dimensions:** h-12 (48px height) with min-width 48px
**Font Size:** 16px (`text-base`)
```tsx
<Button size="mobile-sm">Mobile Small</Button>
```

### 10. `mobile-default` - Mobile Standard ✅ Mobile Optimized
**Dimensions:** h-12 (48px height) with min-width 48px
**Font Size:** 16px (`text-base`)
```tsx
<Button size="mobile-default">Mobile Action</Button>
```

### 11. `mobile-lg` - Mobile Large ✅ Mobile Optimized
**Dimensions:** h-14 (56px height) with min-width 56px
**Font Size:** 18px (`text-lg`)
```tsx
<Button size="mobile-lg">Mobile Large</Button>
```

### 12. `mobile-icon` - Mobile Icon ✅ Mobile Optimized
**Dimensions:** h-12 w-12 (48×48px) with min-width/height 48px
**Font Size:** 16px (`text-base`)
```tsx
<Button size="mobile-icon">
  <Settings className="w-5 h-5" />
</Button>
```

## Accessibility Guidelines ✅ WCAG 2.1/2.2 Compliant

### Touch Target Standards
- **Minimum Size**: All buttons now meet 44×44px minimum touch target requirement
- **Mobile Optimization**: Mobile variants provide 48×48px minimum for enhanced usability
- **Spacing**: Use `wcag-spacing` utility class for proper spacing between adjacent buttons

### Typography Standards
- **Font Size**: Minimum 14px for compact buttons, 16px for standard buttons
- **Responsive Scaling**: All buttons scale typography appropriately across breakpoints
- **Readability**: Text scales up to 200% without loss of functionality

### Mobile Enhancements
- **Touch Optimization**: All buttons include `touch-manipulation` for better mobile performance
- **iOS Zoom Prevention**: 16px minimum font size prevents unwanted zoom on iOS devices
- **Tap Highlight**: Custom tap highlight color for better visual feedback

### Usage Examples

#### WCAG Compliant Button Groups
```tsx
<div className="button-group">
  <Button size="default" className="wcag-spacing">Primary</Button>
  <Button variant="outline" size="default" className="wcag-spacing">Secondary</Button>
</div>
```

#### Mobile-Optimized Actions
```tsx
{/* Use mobile variants on mobile devices */}
<div className="md:hidden">
  <Button variant="mobile-primary" size="mobile-default">Mobile Action</Button>
</div>

{/* Use standard variants on desktop */}
<div className="hidden md:block">
  <Button variant="default" size="default">Desktop Action</Button>
</div>
```

#### Responsive Button Implementation
```tsx
<Button
  size="sm"           // 44px height with responsive font scaling
  className="sm:size-default lg:size-lg"  // Grows on larger screens
>
  Responsive Button
</Button>
```

## Usage Patterns

### Form Submissions
```tsx
<Button type="submit" variant="default" size="default">
  Submit
</Button>
```

### Hero Section CTAs
```tsx
<Button variant="cta" size="cta">
  Start Your Journey
</Button>
```

### Modal Close Buttons
```tsx
<Button variant="ghost" size="icon" aria-label="Close">
  <X className="w-4 h-4" />
</Button>
```

### Navigation Actions
```tsx
<Button variant="outline" size="default">
  Back
</Button>
<Button variant="default" size="default">
  Continue
</Button>
```

### Mobile-Optimized Buttons
```tsx
<Button 
  variant="default" 
  size="lg" 
  className="w-full touch-manipulation min-h-[48px]"
>
  Mobile Action
</Button>
```

## Accessibility Guidelines

### Required Attributes
- Always include `aria-label` for icon-only buttons
- Use `type="submit"` for form submission buttons
- Include `disabled` state when appropriate

### Focus Management
- All buttons automatically include focus states
- Use `focus-visible:ring-2` for custom focus styling
- Ensure keyboard navigation works properly

### Touch Targets
- Minimum 44px touch target for mobile
- Use `touch-manipulation` class for better mobile experience
- Add `min-h-[48px]` for mobile-optimized buttons

## Common Mistakes to Avoid

### ❌ Don't Use Custom Styling
```tsx
// Wrong
<button className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
  Custom Button
</button>
```

### ✅ Use Standardized Component
```tsx
// Correct
<Button variant="default" size="default">
  Standardized Button
</Button>
```

### ❌ Don't Override Core Styles
```tsx
// Wrong
<Button className="bg-red-500 text-yellow-300">
  Bad Override
</Button>
```

### ✅ Use Appropriate Variants
```tsx
// Correct
<Button variant="destructive" size="default">
  Proper Variant
</Button>
```

## Migration Checklist

- [ ] Replace all `<button>` elements with `<Button>` component
- [ ] Remove custom button styling classes
- [ ] Use appropriate variant and size props
- [ ] Add proper accessibility attributes
- [ ] Test keyboard navigation
- [ ] Verify mobile touch targets
- [ ] Ensure consistent theming

## Examples by Context

### Contact Forms
```tsx
<Button type="submit" variant="cta" size="cta" className="w-full sm:w-auto">
  Contact Us
</Button>
```

### Service Pages
```tsx
<Button variant="gradient" size="lg">
  Schedule Consultation
</Button>
```

### Navigation
```tsx
<Button variant="ghost" size="icon" aria-label="Menu">
  <Menu className="w-5 h-5" />
</Button>
```

### Footer Actions
```tsx
<Button variant="outline" size="sm" onClick={scrollToTop}>
  <ArrowUp className="w-4 h-4" />
</Button>
```

This standardized approach ensures consistency, accessibility, and maintainability across the entire application.
